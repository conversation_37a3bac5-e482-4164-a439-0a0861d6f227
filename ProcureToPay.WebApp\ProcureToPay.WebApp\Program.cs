using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure; // For DbContextOptionsBuilder
using Microsoft.Extensions.DependencyInjection; // For IServiceScope
using Microsoft.Extensions.Logging; // For ILogger
using System;
using System.Threading; // For Thread.Sleep
using System.Threading.Tasks; // For Task
// Add other necessary using statements for your specific setup
using ProcureToPay.Domain.Interfaces; // For ITenantProvider
using ProcureToPay.Infrastructure.Identity; // For ApplicationUser
using ProcureToPay.Infrastructure.Persistence; // For ApplicationDbContext and EnhancedMigrationHelper
using ProcureToPay.Infrastructure.Services; // For TenantProvider
using ProcureToPay.WebApp.Components;
using ProcureToPay.WebApp.Components.Account;
using ProcureToPay.WebApp.Services; // For DefaultTenantProvider
using EFCore.NamingConventions; // Required for UseSnakeCaseNamingConvention
// Example: Add using for a type in your Client project if needed
using ProcureToPay.WebApp.Client.Pages; // For Counter

// Make the Program class and Main method explicit to support async
public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

// ****************************************************************
// Add enhanced connection string logging:
try
{
    var cs = builder.Configuration.GetConnectionString("postgresdb");
    if (!string.IsNullOrEmpty(cs))
    {
        // Extract and log just the database name for debugging
        var connStringParts = cs.Split(';');
        var dbNamePart = connStringParts.FirstOrDefault(p => p.StartsWith("Database=", StringComparison.OrdinalIgnoreCase));
        var dbName = dbNamePart?.Substring("Database=".Length);

        Console.WriteLine($"---> WebApp DEBUG: Connection string 'postgresdb' at startup: Found (Value Hidden)");
        Console.WriteLine($"---> WebApp DEBUG: Database name from connection string: {dbName ?? "NOT FOUND"}");
    }
    else
    {
        Console.WriteLine($"---> WebApp DEBUG: Connection string 'postgresdb' at startup: NOT FOUND");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"---> WebApp DEBUG: Error trying to get connection string: {ex.Message}");
}
// ****************************************************************


// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

// Configure DbContext with custom factory to handle scoped tenant provider
var connectionString = builder.Configuration.GetConnectionString("postgresdb");

// Register DbContextOptions as Singleton to avoid scope validation errors
// This is safe because the options themselves are immutable and don't depend on scoped services
builder.Services.AddSingleton<DbContextOptions<ApplicationDbContext>>(provider =>
{
    var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();
    optionsBuilder.UseNpgsql(connectionString, sqlOptions =>
    {
        // Add retry on failure for runtime operations
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 5,
            maxRetryDelay: TimeSpan.FromSeconds(30),
            errorCodesToAdd: null);
    });

    // Configure global snake_case naming convention for PostgreSQL
    optionsBuilder.UseSnakeCaseNamingConvention();
    return optionsBuilder.Options;
});

// Register the regular DbContext as Scoped (for normal DI usage)
builder.Services.AddScoped<ApplicationDbContext>((serviceProvider) =>
{
    var options = serviceProvider.GetRequiredService<DbContextOptions<ApplicationDbContext>>();
    var tenantProvider = serviceProvider.GetRequiredService<ITenantProvider>();
    return new ApplicationDbContext(options, tenantProvider);
});

// Register a custom DbContextFactory that properly handles scoped dependencies
builder.Services.AddSingleton<IDbContextFactory<ApplicationDbContext>>(provider =>
{
    return new ScopedDbContextFactory<ApplicationDbContext>(provider);
});

// Register a factory for creating DbContext instances in singleton contexts
builder.Services.AddSingleton<Func<ApplicationDbContext>>(provider =>
{
    // Return a factory function that creates a new DbContext with proper scoping
    return () => DbContextHelper.CreateDbContext(provider);
});

// Add database error page filter (useful for development)
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

// Add Identity with authentication configuration in one place
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options => options.SignIn.RequireConfirmedAccount = true)
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();

// Configure authentication options
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
});

builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();

// Register the HttpContextAccessor (required for tenant resolution)
builder.Services.AddHttpContextAccessor();

// Register the tenant resolution services
builder.Services.AddSingleton<TenantResolutionStrategy>();
builder.Services.AddScoped<ITenantProvider, TenantProvider>();

// Register the ApplicationDbSeeder
builder.Services.AddScoped<ApplicationDbSeeder>();

// Register the CategoryDbContextFactory
builder.Services.AddSingleton<ProcureToPay.Application.Interfaces.ICategoryDbContextFactory,
                             ProcureToPay.Infrastructure.Persistence.CategoryDbContextFactory>();

// Register application services
builder.Services.AddScoped<ProcureToPay.Application.Features.Categories.Services.ICategoryService,
                           ProcureToPay.Application.Features.Categories.Services.CategoryService>();


// Add detailed error handling for service resolution issues
try
{
    var app = builder.Build();

// Apply migrations programmatically at application startup
// This is especially important for containerized applications like those using .NET Aspire
try
{
    Console.WriteLine("---> WebApp: Starting database migration process using EnhancedMigrationHelper...");

    // Use the EnhancedMigrationHelper to apply migrations with robust retry logic
    bool migrationSuccess = app.Services.ApplyMigrationsWithRetry(maxRetries: 10);

    if (migrationSuccess)
    {
        Console.WriteLine("---> WebApp: Database migrations applied successfully.");

        // Seed application data after successful migration
        try
        {
            Console.WriteLine("---> WebApp: Starting application data seeding...");
            using (var scope = app.Services.CreateScope())
            {
                var seeder = scope.ServiceProvider.GetRequiredService<ApplicationDbSeeder>();
                await seeder.SeedAllAsync();
            }
            Console.WriteLine("---> WebApp: Application data seeding completed successfully.");
        }
        catch (Exception seedEx)
        {
            Console.WriteLine($"---> WebApp WARNING: An error occurred during data seeding: {seedEx.Message}");
            Console.WriteLine($"---> WebApp WARNING: {seedEx.GetType().Name}: {seedEx.Message}");
            if (seedEx.InnerException != null)
            {
                Console.WriteLine($"---> WebApp WARNING: Inner exception: {seedEx.InnerException.Message}");
            }
            // Continue application startup even if seeding fails
        }
    }
    else
    {
        Console.WriteLine("---> WebApp WARNING: Migration process completed but reported failure. The application will continue, but some features may not work correctly.");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"---> WebApp ERROR: An unhandled exception occurred during migration process: {ex.Message}");
    Console.WriteLine($"---> WebApp ERROR: {ex.GetType().Name}: {ex.Message}");
    if (ex.InnerException != null)
    {
        Console.WriteLine($"---> WebApp ERROR: Inner exception: {ex.InnerException.Message}");
    }

    // In a production environment, you might want to continue running the application
    // even if migrations fail, depending on your requirements
    // throw; // Uncomment to stop the application if migrations fail
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    // *** UPDATED LINE BELOW ***
    // Use *any* public type defined in your .Client project assembly.
    // Replace 'Counter' if it doesn't exist or is in a different namespace.
    // Common examples: Counter, Home, or a custom public class you create there.
    .AddAdditionalAssemblies(typeof(ProcureToPay.WebApp.Client.Pages.Counter).Assembly); // Example using Counter

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();


await app.RunAsync();
    }
catch (Exception ex)
{
    // Log the detailed exception information
    Console.WriteLine("FATAL ERROR: Application failed to start");
    Console.WriteLine($"Exception Type: {ex.GetType().FullName}");
    Console.WriteLine($"Message: {ex.Message}");

    // Check for AggregateException to get more details
    if (ex is AggregateException aggEx)
    {
        Console.WriteLine("AggregateException contains the following errors:");
        foreach (var innerEx in aggEx.InnerExceptions)
        {
            Console.WriteLine($"- {innerEx.GetType().FullName}: {innerEx.Message}");
            if (innerEx.InnerException != null)
            {
                Console.WriteLine($"  Inner: {innerEx.InnerException.GetType().FullName}: {innerEx.InnerException.Message}");
            }
        }
    }
    else if (ex.InnerException != null)
    {
        Console.WriteLine($"Inner Exception: {ex.InnerException.GetType().FullName}");
        Console.WriteLine($"Inner Message: {ex.InnerException.Message}");
    }

    // Rethrow to terminate the application
    throw;
}
    }
}
